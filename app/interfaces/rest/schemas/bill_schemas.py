from marshmallow import Schema, fields, validate


class BillDownloadSchema(Schema):
    """
    账单下载请求参数验证模式
    """
    bill_type = fields.String(
        required=True,
        validate=validate.OneOf(['tradebill', 'fundflowbill']),
        error_messages={
            'required': '账单类型不能为空',
            'validator_failed': '账单类型错误，可选值：tradebill（交易账单）、fundflowbill（资金账单）'
        }
    )
    bill_date = fields.String(
        required=True,
        error_messages={
            'required': '账单日期不能为空'
        }
    )